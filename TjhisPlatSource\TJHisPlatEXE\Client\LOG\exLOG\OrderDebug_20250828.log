[2025-08-28 17:34:51.394] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 0 -> 2
[2025-08-28 17:34:51.406] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 1） - MaxOrderNo变化: 0 -> 2
[2025-08-28 17:34:59.564] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 2, 数据库最大: 2, 分配: 3
[2025-08-28 17:34:59.564] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 2 -> 3
[2025-08-28 17:34:59.581] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 1） - MaxOrderNo变化: 2 -> 3
[2025-08-28 17:34:59.581] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:35:02.946] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:35:02.947] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:35:02.948] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:35:02.960] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:35:02.961] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:35:02.961] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:35:02.965] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:35:02.965] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 3, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:35:02.965] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 3
[2025-08-28 17:35:03.092] [CHECKPOINT] [OrderBusiness.Save] 检查点[中药库存检查] - 中药数量: 1
[2025-08-28 17:35:03.481] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 17:35:03.500] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 4, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 17:35:03.504] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 17:35:03.538] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 17:35:03.995] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 3
[2025-08-28 17:35:03.996] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 17:35:04.030] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 3 -> 4
[2025-08-28 17:35:04.031] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 3 -> 4
[2025-08-28 17:35:04.584] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 17:35:04.954] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 17:35:05.156] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 2） - MaxOrderNo变化: 4 -> 4
[2025-08-28 17:35:42.209] [ORDER_SYNC] [2025-8-22] ORDER_NO同步检查 - 内存最大: 4, 数据库最大: 4, 分配: 5
[2025-08-28 17:35:42.209] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 4 -> 5
[2025-08-28 17:35:42.226] [MAXNO] [OrderBusiness.Add] MaxOrderNo递增（内存中医嘱数量: 2） - MaxOrderNo变化: 4 -> 5
[2025-08-28 17:35:42.227] [ORDER] [OrderBusiness.Add] 新增医嘱 - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: , ORDER_CLASS: 
[2025-08-28 17:35:46.112] [CHECKPOINT] [UcDrugPresc.Save] 检查点[开始保存] - 西药保存按钮被点击
[2025-08-28 17:35:46.113] [CHECKPOINT] [UcDrugPresc.Save] 待保存医嘱 - 医嘱数量: 0
[2025-08-28 17:35:46.113] [CHECKPOINT] [UcDrugPresc.Save] 检查点[无医嘱保存] - ordersSave为空或数量为0
[2025-08-28 17:35:46.116] [CHECKPOINT] [UcCdrugPresc.Save] 开始保存 - 中药保存按钮被点击
[2025-08-28 17:35:46.116] [BATCH] [UcCdrugPresc.Save] 待保存医嘱 - 医嘱数量: 1
[2025-08-28 17:35:46.116] [BATCH] [UcCdrugPresc.Save] 待保存医嘱[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:35:46.117] [BATCH] [OrderBusiness.Save] 保存前状态 - 医嘱数量: 1
[2025-08-28 17:35:46.117] [BATCH] [OrderBusiness.Save] 保存前状态[1] - CLINIC_NO: 250717000ADMIN00001, ORDER_NO: 5, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁, ORDER_CLASS: B, STATE: 录入
[2025-08-28 17:35:46.117] [CHECKPOINT] [OrderBusiness.Save] 检查点[开始保存] - 医嘱数量: 1, MaxOrderNo: 5
[2025-08-28 17:35:46.226] [CHECKPOINT] [OrderBusiness.Save] 检查点[中药库存检查] - 中药数量: 1
[2025-08-28 17:35:46.526] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存时ITEM_NO] - ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, ORDER_TEXT: 砂仁
[2025-08-28 17:35:46.547] [CHECKPOINT] [OrderBusiness.Save] 检查点[费用记录处理] - ORDER_TEXT: 砂仁, ORDER_NO: 6, ORDER_SUB_NO: 1, ITEM_NO: 1, STATE: 保存
[2025-08-28 17:35:46.548] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行前] - SQL语句数量: 3
[2025-08-28 17:35:46.621] [CHECKPOINT] [OrderBusiness.Save] 检查点[数据库执行] - 执行结果: True
[2025-08-28 17:35:46.908] [CHECKPOINT] [OrderBusiness.Save] 检查点[保存成功] - 已重置缓存，MaxOrderNo: 5
[2025-08-28 17:35:46.909] [CHECKPOINT] [OrderBusiness.Save] 数据库执行 - 执行结果: True
[2025-08-28 17:35:46.970] [MAXNO] [OrderBusiness.MaxOrderNo.set] 属性直接设置 - MaxOrderNo变化: 5 -> 6
[2025-08-28 17:35:46.970] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 5 -> 6
[2025-08-28 17:35:47.796] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
[2025-08-28 17:35:48.919] [CHECKPOINT] [UcCdrugPresc.Save] 保存成功 - 中药保存成功
[2025-08-28 17:35:49.004] [MAXNO] [OrderBusiness.SetMaxOrderNo] 根据现有医嘱设置（医嘱数量: 3） - MaxOrderNo变化: 6 -> 6
